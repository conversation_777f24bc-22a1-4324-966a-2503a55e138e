<?php
// Auto-generated category index
// Category: writings

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Writings Index';
$meta_description = 'Writings Index - A. A. Chips';
$meta_keywords = 'writings, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'The Birth of the Internet - From ARPANET to TikTok',
    'author' => 'A. A. Chips',
    'date' => '2025-06-06',
    'excerpt' => 'A brief history of the internet, from ARPANET to TikTok.',
    'url' => 'arpanet-internet.php',
    'tags' => 
    array (
      0 => 'internet',
      1 => 'history',
      2 => 'writings',
    ),
    'filename' => 'arpanet-internet',
    'thumbnail' => 'https://images.theconversation.com/files/144166/original/image-20161102-27243-uve388.jpg?ixlib=rb-1.1.0&q=30&auto=format&w=754&h=588&fit=crop&dpr=2',
  ),
  1 => 
  array (
    'title' => 'Embrace Decomposition',
    'author' => 'A. A. Chips',
    'date' => '2025-06-06',
    'excerpt' => 'Exploring the metaphor of mushrooms and decomposition in relation to societal and environmental issues.',
    'url' => 'embrace-decomposition.php',
    'tags' => 
    array (
      0 => 'inspiration',
      1 => 'writings',
      2 => 'mushrooms',
      3 => 'decomposition',
      4 => 'waste',
      5 => 'regeneration',
    ),
    'filename' => 'embrace-decomposition',
    'thumbnail' => 'https://greenseattle.org/wp-content/uploads/2019/12/decomposers.jpg',
  ),
  2 => 
  array (
    'title' => 'The Myth of Choice',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Challenging the notion that homelessness is a choice and exploring the reality of limited options',
    'url' => 'myth-of-choice.php',
    'tags' => 
    array (
      0 => 'homelessness',
      1 => 'advocacy',
      2 => 'choice',
    ),
    'filename' => 'myth-of-choice',
    'thumbnail' => '../../img/rainy-road.jpg',
  ),
  3 => 
  array (
    'title' => 'News in the Digital Age - Why What You Read Matters',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'An exploration of the relationship between economics and journalism, and how it affects the news we consume.',
    'url' => 'what-you-read.php',
    'tags' => 
    array (
      0 => 'journalism',
      1 => 'economics',
      2 => 'news',
    ),
    'filename' => 'what-you-read',
    'thumbnail' => 'https://www.usatoday.com/gcdn/authoring/authoring-images/2023/11/14/USAT/71584950007-q-anon-shaman.jpg?width=1320&height=934&fit=crop&format=pjpg&auto=webp',
  ),
  4 => 
  array (
    'title' => 'Bite-Sized Learning for Everyone: Introducing Knowledge "Chips"',
    'author' => 'A. A. Chips',
    'date' => '2025-02-21',
    'excerpt' => 'Have you ever felt overwhelmed by the internet? You go online to learn something new, and suddenly you\'re drowning in hour-long videos, 50-page articles, and endless clickbait. It\'s like trying to drink from a firehose!',
    'url' => 'bite-sized-learning.php',
    'tags' => 
    array (
      0 => 'professionaldev',
      1 => 'aachips',
      2 => 'markdown',
    ),
    'filename' => 'bite-sized-learning',
    'thumbnail' => NULL,
  ),
  5 => 
  array (
    'title' => 'Here\'s what you can do when you feel unsupported by an organization',
    'author' => 'A. A. Chips',
    'date' => '2023-09-09',
    'excerpt' => 'We\'ve all been there. That feeling of being left to figure things out on your own, whether it\'s a project at work, a personal challenge, or navigating a complex system. When the support you expect isn\'t there, it can be frustrating, demotivating, and even make it harder to succeed.',
    'url' => 'build-your-own-supports.php',
    'tags' => 
    array (
      0 => 'advocacy',
      1 => 'accessibility',
      2 => 'addiction',
      3 => 'CompassionateCities',
    ),
    'filename' => 'build-your-own-supports',
    'thumbnail' => NULL,
  ),
  6 => 
  array (
    'title' => 'Indian Child Welfare Act of 1978 (ICWA)',
    'author' => NULL,
    'date' => '2022-09-07',
    'excerpt' => 'I can\'t think of a law that I apply daily that allows me to do the right thing more often. It\'s crucial and all judges need to understand it and try their best to do the right thing. The Indian Child Welfare Act of 1978, or ICWA, is possibly the most misunderstood and least consistently enforced Federal Act of Legislation by Judges, and yet one of the most important pieces of law in protecting Native American Tribal Cultures.',
    'url' => 'icwa-1978.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'homework',
    ),
    'filename' => 'icwa-1978',
    'thumbnail' => 'http://dss.virginia.gov/globalimage/family/icwa/ICWA-training_title-image.jpg',
  ),
  7 => 
  array (
    'title' => 'Ten Reasons I Hate Working in Restaurants',
    'author' => 'A. A. Chips',
    'date' => '2021-04-06',
    'excerpt' => 'My intention in sharing these observations is not merely to complain but to initiate a discussion about potential structural improvements that could benefit both workers and the industry as a whole.',
    'url' => '10-reasons-i-hate-restaurants.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'aachips',
    ),
    'filename' => '10-reasons-i-hate-restaurants',
    'thumbnail' => 'https://i0.wp.com/worksucks.com/wp-content/uploads/2023/08/577a5-restaurant-work-sucks.jpg',
  ),
  8 => 
  array (
    'title' => 'Nostalgia: A Poem',
    'author' => 'A. A. Chips',
    'date' => '2011-11-27',
    'excerpt' => 'I wrote this poem in 2011 while attending school near a former mental hospital. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.',
    'url' => 'nostalgia.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'poems',
      2 => 'personal',
    ),
    'filename' => 'nostalgia',
    'thumbnail' => 'https://media.istockphoto.com/id/1158425010/photo/cow-with-cowbell-in-an-alpine-meadow-in-the-swiss-alps-in-front-of-a-farm-with-swiss-flag.jpg',
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<p>I write. Not because I like writing or anything. Well it's better than talking. That's for sure. I write to convey experiences and ideas. Sometimes I use Artificial Intelligence to refine what I write. It's because I've been told numerous times I don't have tact. By ex's and family members. The reality is that, I have so much tact. I have the most tact of anyone alive. No one has ever had more tact than I have. So if I write a bit like a robot, it's because there's a chip in my brain. Many chips. More chips than anyone has ever seen. Also lots of worms and bugs, too. But I also have good ideas sometimes. A broken clock is right twice a day, right? I'm not saying I'm a broken clock. Just a clock that's a little slower than the other clocks. Crap, I'm doing it again. So about this list. Some of these items are literal school homework that I hijacked to make my own.</p>

HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>