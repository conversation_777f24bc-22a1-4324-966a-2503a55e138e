<?php
// Auto-generated blog post
// Source: rascal-children-gaza.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Rascal Children of Gaza';
$meta_description = 'Palestinian poet and playwright <PERSON><PERSON><PERSON> was raised in Al-Shaboura Palestinian Refugee Camp, in the Gaza Strip. He is currently Head of the Cultural Department in Palestine News and Information Agency (WAFA) and was previously Editor-in-Chief of Roya Magazine for seven years.';
$meta_keywords = 'jewish, palestine, repost, library, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Rascal Children of Gaza',
  'author' => 'Khaled Juma',
  'date' => '2023-11-08',
  'tags' => 
  array (
    0 => 'jewish',
    1 => 'palestine',
    2 => 'repost',
    3 => 'library',
  ),
  'excerpt' => 'Palestinian poet and playwright Khaled Juma was raised in Al-Shaboura Palestinian Refugee Camp, in the Gaza Strip. He is currently Head of the Cultural Department in Palestine News and Information Agency (WAFA) and was previously Editor-in-Chief of Roya Magazine for seven years.',
  'source_file' => 'content\\judaism\\rascal-children-gaza.md',
);

// Raw content
$post_content = '<img src="https://www.jewishvoiceforlabour.org.uk/app/uploads/2023/11/Gaz-children-swimming-1500x844.jpg" width="400" alt="Children completing their swimming course on North Gaza Beach, 24th August 2023. Image via Paul O\'Brien, SwimWithGaza.">

<p>Children completing their swimming course on North Gaza Beach, 24th August 2023. Image via Paul O\'Brien, SwimWithGaza</p>

<p><a href="https://nerdalicious.com.au/poets-stage/khaled-juma-oh-rascal-children-of-gaza/" target="_blank">Oh Rascal Children of Gaza</a></p>

<p>Khaled Juma</p>

<p><blockquote></p>
<p>Oh rascal children of Gaza,<br><br></p>

<p>You who constantly disturbed me with your screams under my window,<br></p>
<p>You who filled every morning with rush and chaos,<br></p>
<p>You who broke my vase and stole the lonely flower on my balcony,<br><br></p>

<p>Come back –<br><br></p>

<p>And scream as you want,<br></p>
<p>And break all the vases,<br></p>
<p>Steal all the flowers,<br></p>
<p>Come back,<br></p>
<p>Just come back…</p>
<p></blockquote></p>

<p>---</p>

<p>#### JVL Commentary</p>


<img src="https://www.jewishvoiceforlabour.org.uk/app/uploads/2023/11/KhaledJuma-300x300.jpg" width="400" alt="Khaled Juma.">

<p>Palestinian poet and playwright Khaled Juma was raised in Al-Shaboura Palestinian Refugee Camp, in the Gaza Strip. He is currently Head of the Cultural Department in Palestine News and Information Agency (WAFA) and was previously Editor-in-Chief of Roya Magazine for seven years.</p>



<p>Juma’s poem “Oh Rascal Children Of Gaza” was written during the Israeli bombardment of Gaza in Operation Protective Edge and first published on August 24, 2014, illustrated with photographs both of children playing, and ones showing the devastating consequences of the war on children.</p>

<p>The investigation carried out by Defence for Children International, Palestine into child fatalities in this bombardment found overwhelming and repeated evidence that Israeli forces committed grave violations against children amounting to war crimes.</p>

<p>Its report published in April 2015 <a href="https://d3n8a8pro7vhmx.cloudfront.net/dcipalestine/pages/530/attachments/original/1436292897/OPE_A_War_Waged_on_Children.pdf?1436292897" target="_blank">said</a>: “For children living in the Gaza Strip, 2014 was a year that brought violence, fear, and loss. The Israeli military offensive that lasted 50 days between July 8 and August 26, dubbed Operation Protective Edge, killed 547 Palestinian children, 535 of them as a direct result of Israeli attacks. 1 Another 3,374 children suffered injuries in attacks, including over 1,000 children whose wounds rendered them permanently disabled.”</p>

<p>Who could have imagined the scale of devastation we are witnessing now, nine years later. On 7 November this year <a href="https://www.dci-palestine.org/4237_palestinian_children_killed_as_gaza_becomes_graveyard_for_children" target="_blank">DCIP reported</a> that so far Israeli forces have killed 4,237 Palestinian children, and about 1,350 more children are missing under the rubble, most of whom are presumed dead.</p>

<p>Two videos supplied by SwimWithGaza of children completing their swimming course on North Gaza Beach on 24th August 2023. In all, around 1,000 children were trained to swim this Summer at the Tantish Swimming Academy.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>